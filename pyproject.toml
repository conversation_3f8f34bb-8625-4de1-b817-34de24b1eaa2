[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "user-df"
version = "2.0.0"
description = "用户数据处理和向量化系统 - 重构版"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "User-DF Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "User-DF Team", email = "<EMAIL>"}
]
keywords = ["user-data", "vector-database", "mongodb", "milvus", "data-processing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Database",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.8"

# 核心依赖
dependencies = [
    # 配置和日志管理
    "PyYAML>=6.0",
    "coloredlogs>=15.0",
    "python-dotenv>=1.0.0",
    
    # 数据验证和类型检查
    "pydantic>=1.10.0",
    
    # 数据库依赖
    "pymongo>=4.3.0",
    "pymilvus>=2.5.0",
    "redis>=4.5.0",
    
    # 数据处理依赖
    "numpy>=1.21.0",
    "pandas>=1.5.0",
    "scipy>=1.9.0",
    "scikit-learn>=1.1.0",
    "pyarrow>=10.0.0",
    
    # 网络和API依赖
    "requests>=2.28.0",
    
    # 系统监控依赖
    "psutil>=5.9.0",
    
    # 时间处理依赖
    "python-dateutil>=2.8.0",
    "pytz>=2022.1",
    
    # 任务调度依赖
    "schedule>=1.2.0",
]

[project.optional-dependencies]
# 开发依赖
dev = [
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "isort>=5.10.0",
    "pre-commit>=2.20.0",
]

# 测试依赖
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.8.0",
    "pytest-xdist>=2.5.0",
    "factory-boy>=3.2.0",
    "faker>=15.0.0",
    "coverage>=6.0.0",
]

# 文档依赖
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "mkdocs>=1.4.0",
    "mkdocs-material>=8.5.0",
    "myst-parser>=0.18.0",
]

# 监控依赖
monitoring = [
    "memory-profiler>=0.60.0",
    "prometheus-client>=0.14.0",
]

# 高级功能依赖
advanced = [
    "httpx>=0.24.0",
    "aiohttp>=3.8.0",
    "motor>=3.1.0",
    "hiredis>=2.2.0",
    "uvloop>=0.17.0",
]

# 所有可选依赖
all = [
    "user-df[dev,test,docs,monitoring,advanced]"
]

[project.urls]
Homepage = "https://github.com/user-df/User-DF"
Documentation = "https://user-df.readthedocs.io/"
Repository = "https://github.com/user-df/User-DF.git"
"Bug Tracker" = "https://github.com/user-df/User-DF/issues"
Changelog = "https://github.com/user-df/User-DF/blob/main/CHANGELOG.md"

[project.scripts]
user-df-orc-mongodb = "services.orc_mongodb_service.main:main"
user-df-vector-service = "services.user_vector_service.main:main"
user-df-health-check = "scripts.health_check:main"

[tool.setuptools]
packages = ["shared", "services"]
include-package-data = true

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.txt", "*.md"]
"configs" = ["*.yaml", "*.yml"]
"shared" = ["*.yaml", "*.yml"]

[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["shared", "services", "tests"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "pymongo.*",
    "pymilvus.*",
    "redis.*",
    "pandas.*",
    "numpy.*",
    "scipy.*",
    "sklearn.*",
    "pyarrow.*",
    "coloredlogs.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "mongodb: marks tests that require MongoDB",
    "milvus: marks tests that require Milvus",
    "redis: marks tests that require Redis",
]

[tool.coverage.run]
source = ["shared", "services"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".env",
]
per-file-ignores = [
    "__init__.py:F401",
]
