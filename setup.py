#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
User-DF 项目安装配置
版本: 2.0.0 - 重构版
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# 核心依赖（生产环境必需）
install_requires = [
    # 配置和日志管理
    "PyYAML>=6.0",
    "coloredlogs>=15.0",
    "python-dotenv>=1.0.0",

    # 数据验证和类型检查
    "pydantic>=1.10.0",

    # 数据库依赖
    "pymongo>=4.3.0",
    "pymilvus>=2.5.0",
    "redis>=4.5.0",

    # 数据处理依赖
    "numpy>=1.21.0",
    "pandas>=1.5.0",
    "scipy>=1.9.0",
    "scikit-learn>=1.1.0",
    "pyarrow>=10.0.0",

    # 网络和API依赖
    "requests>=2.28.0",

    # 系统监控依赖
    "psutil>=5.9.0",

    # 时间处理依赖
    "python-dateutil>=2.8.0",
    "pytz>=2022.1",

    # 任务调度依赖
    "schedule>=1.2.0",
]

# 开发依赖
dev_requires = [
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "isort>=5.10.0",
]

# 测试依赖
test_requires = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.8.0",
    "pytest-xdist>=2.5.0",
    "factory-boy>=3.2.0",
    "faker>=15.0.0",
]

# 文档依赖
docs_requires = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "mkdocs>=1.4.0",
    "mkdocs-material>=8.5.0",
]

# 监控依赖
monitoring_requires = [
    "memory-profiler>=0.60.0",
]

# 高级功能依赖
advanced_requires = [
    "httpx>=0.24.0",
    "aiohttp>=3.8.0",
    "motor>=3.1.0",
    "hiredis>=2.2.0",
]

setup(
    name="user-df",
    version="2.0.0",
    description="用户数据处理和向量化系统 - 重构版",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="User-DF Team",
    author_email="<EMAIL>",
    url="https://github.com/user-df/User-DF",

    # 包配置
    packages=find_packages(include=["shared*", "services*"]),
    package_dir={
        "shared": "shared",
        "services": "services",
    },

    # Python版本要求
    python_requires=">=3.8",

    # 依赖配置
    install_requires=install_requires,
    extras_require={
        "dev": dev_requires,
        "test": test_requires,
        "docs": docs_requires,
        "monitoring": monitoring_requires,
        "advanced": advanced_requires,
        "all": dev_requires + test_requires + docs_requires + monitoring_requires + advanced_requires,
    },

    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Database",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],

    # 关键词
    keywords="user-data, vector-database, mongodb, milvus, data-processing",

    # 项目URL
    project_urls={
        "Bug Reports": "https://github.com/user-df/User-DF/issues",
        "Source": "https://github.com/user-df/User-DF",
        "Documentation": "https://user-df.readthedocs.io/",
    },

    # 入口点
    entry_points={
        "console_scripts": [
            "user-df-orc-mongodb=services.orc_mongodb_service.main:main",
            "user-df-vector-service=services.user_vector_service.main:main",
        ],
    },

    # 包含的数据文件
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt", "*.md"],
        "configs": ["*.yaml", "*.yml"],
        "shared": ["*.yaml", "*.yml"],
    },

    # 排除的文件
    exclude_package_data={
        "": ["*.pyc", "__pycache__", "*.pyo"],
    },

    # Zip安全
    zip_safe=False,
)