#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务主入口

简化版本的ORC到MongoDB用户数据处理服务主程序：
- 命令行参数解析
- 服务初始化和启动
- 信号处理和优雅关闭
- 健康检查和监控
- 单进程处理模式

作者: User-DF Team
版本: 2.0.0
"""

import sys
import os
import argparse
import signal
from typing import Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger, ExceptionHandler
from shared.utils import TimeUtils
from services.orc_mongodb_service.service import ORCMongoDBService


class ServiceRunner:
    """服务运行器"""

    def __init__(self):
        """初始化服务运行器"""
        self.service: Optional[ORCMongoDBService] = None
        self.logger: Optional[Logger] = None
        self.shutdown_requested = False
        self.signal_count = 0  # 信号计数器，用于强制退出

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, _frame):
        """信号处理器"""
        self.signal_count += 1

        if self.signal_count == 1:
            # 第一次信号：优雅关闭
            if self.logger:
                self.logger.info(f"接收到信号 {signum}，准备关闭服务...")
                self.logger.info("正在停止当前任务，请稍候... (再次按 Ctrl+C 强制退出)")
            self.shutdown_requested = True
        elif self.signal_count == 2:
            # 第二次信号：强制退出
            if self.logger:
                self.logger.warning("接收到第二次中断信号，强制退出服务")
            else:
                print("强制退出服务...")
            sys.exit(1)
        else:
            # 第三次及以上信号：立即退出
            print("立即退出...")
            os._exit(1)
    
    def run(self, args):
        """
        运行服务
        
        Args:
            args: 命令行参数
        """
        try:
            # 初始化配置管理器
            if hasattr(args, 'config') and args.config:
                # 使用指定的配置文件
                config_manager = ConfigManager.create_from_config_file(args.config)
                self.logger = Logger.get_logger("ORCMongoDBServiceRunner")
                self.logger.info(f"使用配置文件: {args.config}")
            else:
                # 使用传统的环境配置方式
                config_manager = ConfigManager()
                # 设置环境
                if args.environment:
                    config_manager.environment = args.environment
                self.logger = Logger.get_logger("ORCMongoDBServiceRunner")
                self.logger.info(f"使用环境配置: {args.environment or 'development'}")

            # 处理省份参数覆盖
            if hasattr(args, 'province_id') and args.province_id is not None:
                # 获取服务名称来设置正确的配置路径
                service_name = "orc_mongodb_service"
                # 覆盖配置中的省份设置
                config_manager.set_config(service_name, 'province_ids', [args.province_id])
                self.logger.info(f"省份参数覆盖: 使用省份ID {args.province_id}")

            self.logger.info("=== ORC MongoDB服务启动 ===")

            # 初始化服务
            self.service = ORCMongoDBService(config_manager)
            
            # 将shutdown_requested标志传递给服务
            self.service.set_shutdown_callback(lambda: self.shutdown_requested)
            
            # 执行健康检查
            if args.health_check:
                self._perform_health_check()
                return
            
            # 根据参数执行不同的处理模式
            if args.date:
                # 处理单个日期
                success = self.service.process_single_date(args.date)
            elif args.start_date and args.end_date:
                # 处理日期范围
                success = self.service.process_date_range(args.start_date, args.end_date)
            elif args.auto_date:
                # 自动处理昨天的数据
                yesterday = TimeUtils.subtract_days(TimeUtils.today(), 1)
                date_str = TimeUtils.format_datetime(
                    datetime.combine(yesterday, datetime.min.time()),
                    'compact_date'
                )
                success = self.service.process_single_date(date_str)
            else:
                # 使用配置文件中的默认日期范围
                config_defaults = load_config_defaults()
                start_date = config_defaults.get('start_date')
                end_date = config_defaults.get('end_date')

                if start_date and end_date:
                    self.logger.info(f"使用配置文件中的日期范围: {start_date} - {end_date}")
                    success = self.service.process_date_range(start_date, end_date)
                elif start_date:
                    self.logger.info(f"使用配置文件中的单个日期: {start_date}")
                    success = self.service.process_single_date(start_date)
                else:
                    self.logger.error("未指定处理日期，且配置文件中也没有默认日期设置")
                    return
            
            # 输出结果
            if success:
                self.logger.info("=== 服务执行成功 ===")
            else:
                self.logger.error("=== 服务执行失败 ===")
                sys.exit(1)
                
        except KeyboardInterrupt:
            self.logger.info("用户中断服务")
        except Exception as e:
            if self.logger:
                self.logger.error(f"服务运行失败: {e}")
            ExceptionHandler.handle_exception(e)
            sys.exit(1)
        finally:
            if self.logger:
                self.logger.info("=== ORC MongoDB服务结束 ===")
    
    def _perform_health_check(self):
        """执行健康检查"""
        try:
            health_status = self.service.health_check()
            
            self.logger.info("=== 健康检查结果 ===")
            self.logger.info(f"服务状态: {health_status.get('status', 'unknown')}")
            
            # MongoDB健康状态
            mongodb_health = health_status.get('mongodb', {})
            self.logger.info(f"MongoDB状态: {mongodb_health.get('status', 'unknown')}")
            
            # 配置健康状态
            config_health = health_status.get('config', {})
            self.logger.info(f"配置状态: {config_health.get('status', 'unknown')}")
            self.logger.info(f"ORC路径存在: {config_health.get('orc_base_path_exists', False)}")
            
            # 统计信息
            stats = health_status.get('stats', {})
            self.logger.info(f"已处理文件: {stats.get('processed_files', 0)}")
            self.logger.info(f"已处理记录: {stats.get('processed_records', 0)}")
            self.logger.info(f"有效用户: {stats.get('valid_users', 0)}")
            
            # 判断整体健康状态
            overall_status = health_status.get('status', 'unknown')
            if overall_status == 'healthy':
                self.logger.info("✅ 服务健康检查通过")
            else:
                self.logger.error("❌ 服务健康检查失败")
                sys.exit(1)
                
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            sys.exit(1)


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="ORC MongoDB服务 - 重构版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 直接运行（使用配置文件中的默认日期范围）
  python3 services/orc_mongodb_service/main.py

  # 处理单个日期
  python3 services/orc_mongodb_service/main.py --date 20240101

  # 处理日期范围
  python3 services/orc_mongodb_service/main.py --start-date 20240101 --end-date 20240107

  # 自动处理昨天的数据
  python3 services/orc_mongodb_service/main.py --auto-date

  # 健康检查
  python3 services/orc_mongodb_service/main.py --health-check

  # 指定环境
  python3 services/orc_mongodb_service/main.py --date 20240101 --environment production

  # 指定省份（覆盖配置文件中的省份设置）
  python3 services/orc_mongodb_service/main.py --province-id 100

  # 组合使用：指定日期和省份
  python3 services/orc_mongodb_service/main.py --date 20240101 --province-id 200

  注意: 此版本为单进程处理模式，已移除多进程相关功能
       默认使用配置文件中的start_date和end_date进行处理
       --province-id参数会覆盖配置文件中的province_ids设置
        """
    )
    
    # 处理模式参数组 - 不再强制要求，支持从配置文件读取默认值
    mode_group = parser.add_mutually_exclusive_group(required=False)
    mode_group.add_argument(
        '--date',
        type=str,
        help='处理指定日期的数据 (格式: YYYYMMDD)'
    )
    mode_group.add_argument(
        '--start-date',
        type=str,
        help='处理日期范围的开始日期 (格式: YYYYMMDD，需要配合--end-date使用)'
    )
    mode_group.add_argument(
        '--auto-date',
        action='store_true',
        help='自动处理昨天的数据'
    )
    mode_group.add_argument(
        '--health-check',
        action='store_true',
        help='执行健康检查'
    )
    mode_group.add_argument(
        '--use-config-dates',
        action='store_true',
        help='使用配置文件中的日期范围 (默认行为)'
    )
    
    # 日期范围结束日期
    parser.add_argument(
        '--end-date',
        type=str,
        help='处理日期范围的结束日期 (格式: YYYYMMDD，需要配合--start-date使用)'
    )
    
    # 环境配置
    parser.add_argument(
        '--environment',
        type=str,
        choices=['development', 'testing', 'production'],
        help='指定运行环境（仅在未指定 --config 时使用）'
    )
    
    # 配置文件路径
    parser.add_argument(
        '--config-dir',
        type=str,
        default='configs',
        help='配置文件目录路径（仅在未指定 --config 时使用）(默认: configs)'
    )

    parser.add_argument(
        '--config',
        type=str,
        help='指定配置文件路径（如果指定，将忽略 --environment 和 --config-dir 参数）'
    )

    # 省份参数覆盖
    parser.add_argument(
        '--province-id',
        type=int,
        help='指定要处理的省份ID，覆盖配置文件中的province_ids设置'
    )
    
    # 日志级别
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    # 详细输出
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细输出'
    )
    
    return parser


def validate_arguments(args):
    """验证命令行参数"""
    # 验证日期范围参数
    if args.start_date and not args.end_date:
        raise ValueError("使用--start-date时必须同时指定--end-date")

    if args.end_date and not args.start_date:
        raise ValueError("使用--end-date时必须同时指定--start-date")

    # 验证日期格式
    date_fields = [args.date, args.start_date, args.end_date]
    for date_field in date_fields:
        if date_field and not TimeUtils.is_valid_date_string(date_field, 'compact_date'):
            raise ValueError(f"无效的日期格式: {date_field}，应为YYYYMMDD格式")

    # 验证日期范围逻辑
    if args.start_date and args.end_date:
        start_days = TimeUtils.date_to_days(args.start_date)
        end_days = TimeUtils.date_to_days(args.end_date)
        if start_days > end_days:
            raise ValueError(f"开始日期 {args.start_date} 不能晚于结束日期 {args.end_date}")


def load_config_defaults():
    """从配置文件加载默认参数"""
    try:
        config_manager = ConfigManager()
        service_config = config_manager.get_config("orc_mongodb_service", default={})

        return {
            'start_date': service_config.get('start_date'),
            'end_date': service_config.get('end_date'),
        }
    except Exception as e:
        print(f"警告: 无法加载配置文件默认值: {e}")
        return {}


def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()

        # 如果没有指定任何处理模式，默认使用配置文件中的日期
        if not any([args.date, args.start_date, args.auto_date, args.health_check, args.use_config_dates]):
            args.use_config_dates = True

        # 验证参数
        validate_arguments(args)

        # 设置环境变量（仅在未指定--config时使用）
        if not (hasattr(args, 'config') and args.config):
            if args.config_dir:
                os.environ['USER_DF_CONFIG_DIR'] = args.config_dir
            if args.environment:
                os.environ['USER_DF_ENV'] = args.environment

        # 创建并运行服务
        runner = ServiceRunner()
        runner.run(args)

    except Exception as e:
        print(f"启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
