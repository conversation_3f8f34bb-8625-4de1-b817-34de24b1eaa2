#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的ORC文件读取器

直接使用pyarrow.orc读取ORC文件，不依赖Hive连接：
- 读取和解析ORC文件
- 文件发现和列表
- 基本的数据验证

作者: User-DF Team
版本: 2.0.0
"""

import os
import glob
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
import pandas as pd
import pyarrow as pa
import pyarrow.orc as orc

from shared.core import Logger, ExceptionHandler


@dataclass
class ORCFileInfo:
    """ORC文件信息"""
    file_path: str
    file_size: int
    num_rows: int
    num_columns: int
    schema: Dict[str, str]


@dataclass
class ReadOptions:
    """读取选项"""
    columns: Optional[List[str]] = None
    batch_size: Optional[int] = None
    use_threads: bool = True


class SimpleORCReader:
    """简化的ORC文件读取器"""
    
    def __init__(self, logger_name: str = "SimpleORCReader"):
        """
        初始化ORC读取器
        
        Args:
            logger_name: 日志器名称
        """
        self.logger = Logger.get_logger(logger_name)
        
    def read_orc_file(self, file_path: str, 
                     options: Optional[ReadOptions] = None) -> pd.DataFrame:
        """
        读取ORC文件
        
        Args:
            file_path: ORC文件路径
            options: 读取选项
            
        Returns:
            DataFrame
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ORC文件不存在: {file_path}")

            # 设置默认选项
            if options is None:
                options = ReadOptions()

            # 尝试读取ORC文件（让pyarrow自己验证文件格式）
            try:
                orc_file = orc.ORCFile(file_path)
            except Exception as e:
                raise ValueError(f"无法解析为ORC文件: {file_path}, 错误: {e}")
            
            # 构建读取参数
            read_kwargs = {}
            if options.columns:
                read_kwargs["columns"] = options.columns

            # 读取数据 - pyarrow.orc.ORCFile.read() 不支持 use_threads 参数
            table = orc_file.read(**read_kwargs)

            # 转换为pandas时可以使用use_threads参数
            pandas_kwargs = {}
            if options.use_threads is not None:
                pandas_kwargs["use_threads"] = options.use_threads

            df = table.to_pandas(**pandas_kwargs)
            
            self.logger.debug(f"成功读取ORC文件: {file_path}, 行数: {len(df)}")
            return df
            
        except Exception as e:
            self.logger.error(f"读取ORC文件失败: {file_path}, {e}")
            raise
    
    def list_orc_files(self, directory: str, 
                      pattern: str = "*.orc",
                      recursive: bool = False) -> List[str]:
        """
        列出目录中的ORC文件
        
        Args:
            directory: 目录路径
            pattern: 文件匹配模式
            recursive: 是否递归搜索
            
        Returns:
            ORC文件路径列表
        """
        try:
            if not os.path.exists(directory):
                self.logger.warning(f"目录不存在: {directory}")
                return []
            
            # 构建搜索模式
            if recursive:
                search_pattern = os.path.join(directory, "**", pattern)
                orc_files = glob.glob(search_pattern, recursive=True)
            else:
                search_pattern = os.path.join(directory, pattern)
                orc_files = glob.glob(search_pattern)
            
            # 过滤并排序 - 验证是否为有效的ORC文件
            valid_orc_files = []
            for f in orc_files:
                if os.path.isfile(f):
                    # 尝试验证是否为有效的ORC文件
                    if self.validate_orc_file(f):
                        valid_orc_files.append(f)
                    else:
                        self.logger.debug(f"跳过无效的ORC文件: {f}")

            valid_orc_files.sort()
            orc_files = valid_orc_files
            
            self.logger.debug(f"在目录 {directory} 中找到 {len(orc_files)} 个ORC文件")
            return orc_files
            
        except Exception as e:
            self.logger.error(f"列出ORC文件失败: {directory}, {e}")
            return []
    
    def get_file_info(self, file_path: str) -> Optional[ORCFileInfo]:
        """
        获取ORC文件信息
        
        Args:
            file_path: ORC文件路径
            
        Returns:
            文件信息或None
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            # 读取ORC文件元数据
            orc_file = orc.ORCFile(file_path)
            
            # 获取schema信息
            schema = orc_file.schema
            schema_dict = {}
            for i, field in enumerate(schema):
                schema_dict[field.name] = str(field.type)
            
            # 获取行数
            num_rows = orc_file.nrows
            num_columns = len(schema)
            
            return ORCFileInfo(
                file_path=file_path,
                file_size=file_size,
                num_rows=num_rows,
                num_columns=num_columns,
                schema=schema_dict
            )
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {file_path}, {e}")
            return None
    
    def validate_orc_file(self, file_path: str) -> bool:
        """
        验证ORC文件是否有效
        
        Args:
            file_path: ORC文件路径
            
        Returns:
            是否有效
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            # 尝试打开文件
            orc_file = orc.ORCFile(file_path)
            
            # 检查基本属性
            if orc_file.nrows < 0:
                return False
            
            if len(orc_file.schema) == 0:
                return False
            
            return True
            
        except Exception as e:
            self.logger.debug(f"ORC文件验证失败: {file_path}, {e}")
            return False
    
    def read_orc_batch(self, file_path: str, 
                      batch_size: int = 10000,
                      columns: Optional[List[str]] = None) -> List[pd.DataFrame]:
        """
        批量读取ORC文件
        
        Args:
            file_path: ORC文件路径
            batch_size: 批次大小
            columns: 要读取的列
            
        Returns:
            DataFrame批次列表
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ORC文件不存在: {file_path}")
            
            # 读取完整数据
            options = ReadOptions(columns=columns, use_threads=True)
            df = self.read_orc_file(file_path, options)
            
            # 分批返回
            batches = []
            for i in range(0, len(df), batch_size):
                batch_df = df.iloc[i:i + batch_size].copy()
                batches.append(batch_df)
            
            self.logger.debug(f"文件 {file_path} 分为 {len(batches)} 个批次")
            return batches
            
        except Exception as e:
            self.logger.error(f"批量读取ORC文件失败: {file_path}, {e}")
            return []
