# ORC MongoDB服务 - 重构版

## 概述

重构版本的ORC到MongoDB用户数据处理服务，使用新的共享模块架构，提供更好的可维护性、可扩展性和性能。

## 主要特性

### 🔄 架构重构
- **共享模块架构**: 使用统一的配置、日志、数据库连接管理
- **模块化设计**: 清晰的职责分离和组件解耦
- **统一异常处理**: 标准化的错误处理和重试机制

### 📊 数据处理
- **ORC文件读取**: 高效的ORC文件批量读取和处理
- **数据验证**: 严格的UID、PID数据验证
- **数据优化**: PID去重、数量限制、时间戳分组
- **批量处理**: 支持大规模数据的批量处理

### 🗄️ 数据库操作
- **连接池管理**: MongoDB连接池复用和健康检查
- **批量写入**: 优化的批量upsert操作
- **分片支持**: 支持MongoDB分片集群
- **事务支持**: 可选的事务处理

### ⚡ 性能优化
- **多进程处理**: 支持多进程并行处理
- **内存优化**: 智能内存管理和垃圾回收
- **I/O优化**: 多线程读取和缓冲优化
- **批量操作**: 减少数据库交互次数

### 📈 监控和日志
- **实时监控**: 处理进度和性能指标监控
- **详细日志**: 结构化日志和错误追踪
- **健康检查**: 服务和依赖组件健康状态检查
- **统计报告**: 详细的处理统计和性能报告

## 目录结构

```
services/orc_mongodb_service/
├── __init__.py              # 模块初始化
├── main.py                  # 服务主入口
├── service.py               # 主服务类
├── processors.py            # ORC数据处理器
├── test_service.py          # 测试脚本
└── README.md               # 说明文档
```

## 配置文件

服务配置位于 `configs/orc_mongodb_service/`，包含：

- **ORC文件配置**: 文件路径、匹配模式、列名映射
- **数据处理配置**: 批次大小、PID限制、验证规则
- **分批处理配置**: 内存优化、分批大小、处理策略
- **并发配置**: 进程数量、队列大小、超时设置
- **监控配置**: 进度报告、统计输出、性能监控
- **优化配置**: 内存优化、I/O优化、数据库优化

### 分批处理配置

```yaml
# 分批处理配置（简化配置）- 核心参数
batch_processing:
  # 用户处理批次大小（核心参数）
  batch_size: 1000
  # PID查询批次大小（发送给Milvus）
  pid_query_batch_size: 1000
  # 是否启用批量优化
  enable_batch_optimization: true
```

**配置说明**:
- `batch_size`: 用户处理批次大小，控制每批处理的用户数量和内存使用
- `pid_query_batch_size`: PID查询批次大小，影响Milvus查询效率
- `enable_batch_optimization`: 是否启用批量PID查询优化

**统一分批处理**:
- 所有文件都使用分批处理模式，确保内存使用可控
- 根据配置参数自动调整批次大小以适应不同环境
- 开发环境使用较小批次便于调试，生产环境使用较大批次提高性能

## 使用方法

### 安装依赖

```bash
# 安装项目依赖
pip install -r requirements.txt

# 或使用项目安装
pip install -e .
```

### 基本使用

```bash
# 处理单个日期
python -m services.orc_mongodb_service.main --date 20240101

# 处理日期范围
python -m services.orc_mongodb_service.main --start-date 20240101 --end-date 20240107

# 自动处理昨天的数据
python -m services.orc_mongodb_service.main --auto-date

# 健康检查
python -m services.orc_mongodb_service.main --health-check
```

### 环境配置

```bash
# 指定运行环境
python -m services.orc_mongodb_service.main --date 20240101 --environment production

# 指定配置目录
python -m services.orc_mongodb_service.main --date 20240101 --config-dir /path/to/configs

# 设置日志级别
python -m services.orc_mongodb_service.main --date 20240101 --log-level DEBUG
```

### 测试服务

```bash
# 运行基础测试脚本
python services/orc_mongodb_service/test_service.py

# 测试分批处理功能
python services/orc_mongodb_service/test_chunked_processing.py
```

### 性能调优建议

**内存优化**:
- 统一使用分批处理模式，内存使用量可预测和控制
- 简化的批次配置参数：
  - `batch_size`: 控制每批处理的用户数量，直接影响内存使用
  - `pid_query_batch_size`: 控制Milvus查询批次，影响查询性能
- 开发环境: batch_size=1000, pid_query_batch_size=1000
- 生产环境: batch_size=1000, pid_query_batch_size=15000

**性能监控**:
```bash
# 监控内存使用
python -c "
import psutil
import time
while True:
    mem = psutil.virtual_memory()
    print(f'内存使用: {mem.percent}%, 可用: {mem.available/1024/1024/1024:.1f}GB')
    time.sleep(5)
"
```

## 数据流程

### 统一分批处理流程 ⭐
服务对所有文件统一使用分批处理模式，确保内存使用可控：

**分批处理流程**:
1. **文件读取**: 读取整个ORC文件到内存
2. **用户聚合**: 处理所有记录，按用户聚合PID数据
3. **分批处理**: 按 `users_per_chunk`（1000个用户）分批处理
4. **批次优化**: 每批用户进行PID去重、Milvus查询（批次大小15000）、数量截取（300个PID）
5. **立即写入**: 每批处理完成后立即写入MongoDB
6. **内存释放**: 每批处理完成后释放内存，开始下一批

**统一分批处理优势**:
- 🔄 **统一模式**: 所有文件使用相同处理策略，简化维护
- 🚀 **内存优化**: 显著降低内存占用，避免OOM问题
- ⚡ **性能稳定**: 避免因内存不足导致的性能下降
- 🎯 **可预测**: 内存使用量可预测和控制
- 📊 **进度可见**: 实时显示处理进度和内存使用情况
- 🔧 **易调优**: 通过配置参数精确控制批次大小

## 数据格式

### 输入数据 (ORC文件)
```
uid: int          # 用户ID (0-20亿)
pid: string       # 产品ID
timestamp: int    # 时间戳（天数格式）
provid: string    # 提供商ID (可选)
```

### 输出数据 (MongoDB)
```json
{
  "_id": 12345,
  "pid_groups": [
    {
      "timestamp_days": 19000,
      "pids": ["pid_1", "pid_2"]
    },
    {
      "timestamp_days": 19001,
      "pids": ["pid_3"]
    }
  ],
  "pid_count": 3,
  "created_days": 19000,
  "updated_days": 19000,
  "vector_status": {
    "is_stored": false,
    "stored_at_days": null
  },
  "prov_id": "100"
}
```

## 性能特性

### 处理能力
- **吞吐量**: 支持每秒处理数万条记录
- **并发**: 支持多进程并行处理
- **内存**: 优化的内存使用，支持大文件处理
- **扩展**: 支持水平扩展和分布式处理

### 优化策略
- **批量操作**: 减少数据库交互次数
- **连接池**: 复用数据库连接
- **内存管理**: 智能垃圾回收和内存释放
- **I/O优化**: 多线程读取和缓冲

## 监控和告警

### 监控指标
- 处理速度（记录/秒）
- 内存使用率
- CPU使用率
- 数据库连接状态
- 错误率和成功率

### 日志级别
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息和进度报告
- **WARNING**: 警告信息和可恢复错误
- **ERROR**: 错误信息和异常
- **CRITICAL**: 严重错误和系统故障

## 故障排除

### 常见问题

1. **ORC文件读取失败**
   - 检查文件路径和权限
   - 验证ORC文件格式
   - 查看详细错误日志

2. **MongoDB连接失败**
   - 检查MongoDB服务状态
   - 验证连接配置
   - 检查网络连接

3. **内存不足**
   - 调整批次大小
   - 启用内存优化
   - 增加系统内存

4. **处理速度慢**
   - 增加并发进程数
   - 优化批次大小
   - 检查数据库性能

### 调试方法

```bash
# 启用详细日志
python -m services.orc_mongodb_service.main --date 20240101 --log-level DEBUG --verbose

# 健康检查
python -m services.orc_mongodb_service.main --health-check

# 运行测试
python services/orc_mongodb_service/test_service.py
```

## 版本历史

### v2.0.0 (重构版)
- 使用共享模块架构
- 统一配置和日志管理
- 优化数据处理性能
- 增强错误处理和监控
- 支持多进程并行处理

### v1.0.0 (原版)
- 基础ORC到MongoDB处理功能
- 简单的配置和日志
- 单进程处理模式

## 贡献指南

1. 遵循项目的代码规范
2. 添加适当的测试用例
3. 更新相关文档
4. 提交前运行测试脚本

## 许可证

本项目采用 MIT 许可证。
